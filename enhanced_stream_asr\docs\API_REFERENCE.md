# Enhanced Stream ASR API 参考文档

## 📖 目录

1. [API概述](#api概述)
2. [认证和安全](#认证和安全)
3. [WebSocket API](#websocket-api)
4. [HTTP REST API](#http-rest-api)
5. [错误处理](#错误处理)
6. [客户端SDK](#客户端sdk)
7. [示例代码](#示例代码)
8. [性能指标](#性能指标)

---

## 🎯 API概述

Enhanced Stream ASR 提供两种主要的API接口：

- **WebSocket API**: 用于实时流式语音识别
- **HTTP REST API**: 用于系统管理、配置和监控

### 基础信息

| 项目 | 值 |
|------|-----|
| **基础URL** | `http://localhost:8080` |
| **WebSocket URL** | `ws://localhost:8080/ws/stream` |
| **API版本** | v1.0 |
| **支持格式** | JSON |
| **字符编码** | UTF-8 |

### 通用响应格式

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": **********.123,
  "request_id": "req_123456789"
}
```

---

## 🔐 认证和安全

### API密钥认证

```http
Authorization: Bearer YOUR_API_KEY
```

### 请求限制

| 限制类型 | 限制值 |
|----------|--------|
| **并发连接** | 200/IP |
| **请求频率** | 1000/分钟 |
| **音频时长** | 60分钟/会话 |
| **文件大小** | 100MB |

---

## 🔌 WebSocket API

### 连接建立

```javascript
const ws = new WebSocket('ws://localhost:8080/ws/stream');
```

### 消息协议

#### 1. 握手阶段

**客户端发送握手请求:**

```json
{
  "type": "handshake_request",
  "client_id": "client_123",
  "language": "zh",
  "auto_language_detection": true,
  "sample_rate": 16000,
  "channels": 1,
  "sample_width": 2,
  "chunk_duration": 0.4,
  "enable_intermediate_result": true,
  "enable_punctuation": true,
  "enable_itn": true,
  "custom_separator": null,
  "hotwords": ["科技", "人工智能"],
  "timestamp": **********.123
}
```

**服务器响应:**

```json
{
  "type": "handshake_response",
  "status": "success",
  "session_id": "sess_456789",
  "server_config": {
    "supported_languages": ["zh", "en", "ru"],
    "max_audio_duration": 3600,
    "chunk_size": 6400
  },
  "message": "握手成功",
  "timestamp": **********.456
}
```

#### 2. 音频数据传输

**发送音频数据:**

```json
{
  "type": "audio_data",
  "session_id": "sess_456789",
  "sequence_id": 1,
  "audio_data": "base64_encoded_pcm_data",
  "is_final": false,
  "timestamp": 1640995201.123
}
```

**接收识别结果:**

```json
{
  "type": "recognition_result",
  "session_id": "sess_456789",
  "text": "你好世界",
  "language": "zh",
  "confidence": 0.95,
  "is_final": true,
  "processing_time": 45.2,
  "timestamp": 1640995201.456
}
```

#### 3. 中间结果

```json
{
  "type": "intermediate_result",
  "session_id": "sess_456789",
  "text": "你好",
  "language": "zh",
  "confidence": 0.85,
  "is_final": false,
  "timestamp": 1640995201.200
}
```

#### 4. 状态更新

```json
{
  "type": "status_update",
  "session_id": "sess_456789",
  "state": "processing",
  "message": "正在处理语音数据",
  "timestamp": 1640995201.300
}
```

#### 5. 错误消息

```json
{
  "type": "error",
  "session_id": "sess_456789",
  "error_code": 2001,
  "message": "音频格式不支持",
  "details": {
    "expected_format": "PCM 16kHz 16bit",
    "received_format": "unknown"
  },
  "timestamp": 1640995201.400
}
```

#### 6. 断开连接

```json
{
  "type": "disconnect_request",
  "session_id": "sess_456789",
  "reason": "用户主动断开",
  "timestamp": 1640995202.000
}
```

### WebSocket状态码

| 状态码 | 含义 | 描述 |
|--------|------|------|
| 1000 | 正常关闭 | 会话正常结束 |
| 1001 | 服务器关闭 | 服务器主动关闭连接 |
| 1002 | 协议错误 | WebSocket协议错误 |
| 1003 | 数据类型错误 | 不支持的数据类型 |
| 1011 | 服务器错误 | 服务器内部错误 |

---

## 🌐 HTTP REST API

### 1. 系统信息

#### 获取系统状态

```http
GET /api/status
```

**响应:**

```json
{
  "success": true,
  "data": {
    "server": {
      "version": "1.0.0",
      "uptime": 3600,
      "status": "running"
    },
    "resources": {
      "cpu_usage": 45.2,
      "memory_usage": 2048,
      "disk_usage": 15.6
    },
    "sessions": {
      "active": 5,
      "total": 127
    }
  }
}
```

#### 健康检查

```http
GET /api/health
```

**响应:**

```json
{
  "status": "healthy",
  "checks": {
    "database": "healthy",
    "models": "healthy",
    "memory": "healthy"
  },
  "timestamp": **********.123
}
```

### 2. 语种管理

#### 获取支持的语种

```http
GET /api/languages
```

**响应:**

```json
{
  "success": true,
  "data": {
    "languages": [
      {
        "code": "zh",
        "name": "中文",
        "separator": "，",
        "features": {
          "punctuation": true,
          "itn": true,
          "hotwords": true
        }
      }
    ]
  }
}
```

#### 获取特定语种信息

```http
GET /api/languages/{language_code}
```

### 3. 会话管理

#### 获取活跃会话

```http
GET /api/sessions
```

#### 获取会话详情

```http
GET /api/sessions/{session_id}
```

#### 强制断开会话

```http
DELETE /api/sessions/{session_id}
```

### 4. 监控和指标

#### 获取性能指标

```http
GET /api/metrics
```

#### 获取实时统计

```http
GET /api/stats/realtime
```

---

## ⚠️ 错误处理

### 错误码分类

| 范围 | 类型 | 描述 |
|------|------|------|
| 1000-1999 | 握手错误 | 连接建立阶段的错误 |
| 2000-2999 | 数据传输错误 | 音频数据处理错误 |
| 3000-3999 | 会话管理错误 | 会话状态相关错误 |
| 4000-4999 | 系统错误 | 服务器内部错误 |
| 5000-5999 | 协议错误 | 通信协议错误 |

### 常见错误码

| 错误码 | 错误名称 | 描述 | 解决方案 |
|--------|----------|------|----------|
| 1001 | INVALID_CLIENT_ID | 无效的客户端ID | 检查客户端ID格式 |
| 1002 | UNSUPPORTED_LANGUAGE | 不支持的语种 | 使用支持的语种代码 |
| 2001 | INVALID_AUDIO_FORMAT | 音频格式错误 | 使用PCM 16kHz 16bit格式 |
| 2002 | AUDIO_TOO_LONG | 音频过长 | 分段发送或减少音频长度 |
| 3001 | SESSION_NOT_FOUND | 会话不存在 | 检查会话ID或重新建立连接 |
| 3002 | SESSION_EXPIRED | 会话已过期 | 重新建立会话 |
| 4001 | INTERNAL_SERVER_ERROR | 服务器内部错误 | 联系技术支持 |
| 5001 | PROTOCOL_VERSION_MISMATCH | 协议版本不匹配 | 更新客户端版本 |

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": 2001,
    "name": "INVALID_AUDIO_FORMAT",
    "message": "音频格式不支持",
    "details": {
      "expected": "PCM 16kHz 16bit",
      "received": "unknown"
    }
  },
  "timestamp": **********.123,
  "request_id": "req_123456789"
}
```

---

## 📱 客户端SDK

### JavaScript SDK

#### 安装

```bash
npm install enhanced-stream-asr-js
```

#### 基本使用

```javascript
import { ASRClient } from 'enhanced-stream-asr-js';

const client = new ASRClient({
  serverUrl: 'ws://localhost:8080/ws/stream',
  apiKey: 'your-api-key'
});

// 连接到服务器
await client.connect({
  language: 'zh',
  autoLanguageDetection: true,
  enableIntermediateResult: true
});

// 设置事件监听
client.on('result', (result) => {
  console.log('识别结果:', result.text);
});

client.on('error', (error) => {
  console.error('错误:', error);
});

// 开始录音
await client.startRecording();

// 停止录音
await client.stopRecording();

// 断开连接
await client.disconnect();
```

### Python SDK

#### 安装

```bash
pip install enhanced-stream-asr-python
```

#### 基本使用

```python
import asyncio
from enhanced_stream_asr import ASRClient

async def main():
    client = ASRClient(
        server_url="ws://localhost:8080/ws/stream",
        api_key="your-api-key"
    )

    # 设置事件回调
    def on_result(result):
        print(f"识别结果: {result['text']}")

    def on_error(error):
        print(f"错误: {error}")

    client.on_result = on_result
    client.on_error = on_error

    # 连接到服务器
    await client.connect({
        "language": "zh",
        "auto_language_detection": True,
        "enable_intermediate_result": True
    })

    # 发送音频文件
    await client.send_audio_file("audio.wav")

    # 断开连接
    await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
```

---

## 📊 性能指标

### 响应时间

| 操作 | 平均响应时间 | P95响应时间 |
|------|-------------|-------------|
| WebSocket连接 | 50ms | 100ms |
| 握手协商 | 30ms | 60ms |
| 音频处理 | 85ms | 150ms |
| 语种识别 | 200ms | 400ms |

### 吞吐量

| 指标 | 值 |
|------|-----|
| 最大并发连接 | 200+ |
| 音频处理能力 | 1000小时/天 |
| 请求处理能力 | 10000请求/分钟 |

### 准确率

| 语种 | 准确率 | 测试数据集 |
|------|--------|-----------|
| 中文 | 95.2% | 10000小时 |
| 英文 | 94.8% | 8000小时 |
| 俄文 | 92.1% | 5000小时 |

---

## 📞 技术支持

### 联系方式

- **技术支持邮箱**: <EMAIL>
- **开发者社区**: https://github.com/your-org/enhanced-stream-asr/discussions
- **问题反馈**: https://github.com/your-org/enhanced-stream-asr/issues

### 文档更新

本文档会定期更新，最新版本请访问：
https://docs.enhanced-asr.com/api-reference

---

*最后更新时间: 2025-06-25*
