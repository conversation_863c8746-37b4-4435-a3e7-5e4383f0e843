# Enhanced Stream ASR Server - CPU版本
FROM continuumio/miniconda3:4.12.0

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV CONDA_DEFAULT_ENV=asr_env

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    cmake \
    git \
    wget \
    curl \
    libsndfile1 \
    libsndfile1-dev \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 创建conda环境
RUN conda create -n asr_env python=3.8 -y

# 激活环境并安装基础包
SHELL ["conda", "run", "-n", "asr_env", "/bin/bash", "-c"]

# 升级pip和安装wheel
RUN pip install --upgrade pip setuptools wheel

# 复制requirements文件
COPY requirements.txt /app/

# 安装Python依赖 (CPU版本)
RUN pip install torch==2.0.1+cpu torchaudio==2.0.2+cpu -f https://download.pytorch.org/whl/torch_stable.html && \
    pip install onnxruntime==1.16.3 && \
    pip install -r requirements.txt

# 复制项目文件
COPY . /app/

# 创建必要的目录
RUN mkdir -p /app/logs /app/models /app/temp

# 设置权限
RUN chmod +x /app/server.py /app/start.py

# 暴露端口
EXPOSE 8000 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["conda", "run", "-n", "asr_env", "python", "server.py"]
