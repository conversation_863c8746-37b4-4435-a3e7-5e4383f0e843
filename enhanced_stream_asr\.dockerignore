# Git相关
.git
.gitignore
.gitattributes

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/
*.out

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 文档构建
docs/_build/
site/

# 模型文件（太大，应该单独挂载）
models/
*.onnx
*.pt
*.pth
*.bin
*.model

# 数据文件
data/
datasets/
*.wav
*.mp3
*.flac
*.ogg

# 配置文件中的敏感信息
.env
.env.local
.env.production
secrets/
*.key
*.pem
*.crt

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 构建相关
build.py
setup.py
*.spec
deploy/

# 开发工具
.flake8
.black
.isort.cfg
pyproject.toml

# 监控配置
monitoring/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 其他
README.md
LICENSE
CHANGELOG.md
TODO.md
