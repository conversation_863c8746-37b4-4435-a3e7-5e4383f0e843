# Enhanced Stream ASR Server - PowerShell Build Script
# 用于Windows PowerShell的构建脚本

param(
    [switch]$All,
    [switch]$Clean,
    [switch]$Cython,
    [switch]$Wheel,
    [switch]$Docker,
    [switch]$Deploy,
    [switch]$Help
)

function Write-Step {
    param([string]$StepName)
    Write-Host ""
    Write-Host ("=" * 60) -ForegroundColor Cyan
    Write-Host "步骤: $StepName" -ForegroundColor Yellow
    Write-Host ("=" * 60) -ForegroundColor Cyan
}

function Invoke-Command-Safe {
    param(
        [string]$Command,
        [string]$Description = ""
    )
    
    if ($Description) {
        Write-Host "执行: $Description" -ForegroundColor Green
    }
    Write-Host "命令: $Command" -ForegroundColor Gray
    
    try {
        $result = Invoke-Expression $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ 失败 (退出码: $LASTEXITCODE)" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ 异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Show-Help {
    Write-Host "Enhanced Stream ASR Server - PowerShell Build Script" -ForegroundColor Cyan
    Write-Host "=================================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法: .\build.ps1 [选项]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  -All      构建所有内容 (默认)" -ForegroundColor White
    Write-Host "  -Clean    清理构建目录" -ForegroundColor White
    Write-Host "  -Cython   仅构建Cython扩展" -ForegroundColor White
    Write-Host "  -Wheel    仅构建wheel包" -ForegroundColor White
    Write-Host "  -Docker   仅构建Docker镜像" -ForegroundColor White
    Write-Host "  -Deploy   仅创建部署包" -ForegroundColor White
    Write-Host "  -Help     显示此帮助信息" -ForegroundColor White
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1 -All      # 构建所有内容" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -Clean    # 清理构建目录" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -Cython   # 仅构建Cython扩展" -ForegroundColor Gray
}

function Test-Prerequisites {
    Write-Step "环境检查"
    
    # 检查Python
    try {
        $pythonVersion = python --version 2>&1
        Write-Host "✓ Python: $pythonVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Python 未安装或不在PATH中" -ForegroundColor Red
        return $false
    }
    
    # 检查pip
    try {
        $pipVersion = pip --version 2>&1
        Write-Host "✓ pip 可用" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ pip 不可用" -ForegroundColor Red
        return $false
    }
    
    # 检查Docker (可选)
    try {
        $dockerVersion = docker --version 2>&1
        Write-Host "✓ Docker: $dockerVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠ Docker 不可用 (将跳过Docker构建)" -ForegroundColor Yellow
    }
    
    return $true
}

function Build-Clean {
    Write-Step "清理构建目录"
    return Invoke-Command-Safe "python build.py --clean" "清理构建文件"
}

function Install-Dependencies {
    Write-Step "安装构建依赖"
    
    $dependencies = @(
        "pip install --upgrade pip setuptools wheel",
        "pip install Cython>=0.29.0",
        "pip install numpy>=1.24.0"
    )
    
    foreach ($dep in $dependencies) {
        if (-not (Invoke-Command-Safe $dep "安装依赖")) {
            return $false
        }
    }
    
    return $true
}

function Build-Cython {
    Write-Step "构建Cython扩展"
    return Invoke-Command-Safe "python build.py --cython" "编译Cython扩展"
}

function Build-Wheel {
    Write-Step "构建Python包"
    return Invoke-Command-Safe "python build.py --wheel" "构建wheel包"
}

function Build-Docker {
    Write-Step "构建Docker镜像"
    
    # 检查Docker是否可用
    try {
        docker info | Out-Null
        Write-Host "Docker 服务正在运行" -ForegroundColor Green
    }
    catch {
        Write-Host "Docker 服务未运行或不可用" -ForegroundColor Red
        return $false
    }
    
    return Invoke-Command-Safe "python build.py --docker" "构建Docker镜像"
}

function Build-Deploy {
    Write-Step "创建部署包"
    return Invoke-Command-Safe "python build.py --deploy" "创建部署包"
}

function Show-Results {
    Write-Step "构建结果"
    
    Write-Host "构建完成！生成的文件:" -ForegroundColor Green
    
    # 检查dist目录
    if (Test-Path "dist") {
        Write-Host ""
        Write-Host "Python包:" -ForegroundColor Yellow
        Get-ChildItem "dist" | ForEach-Object {
            Write-Host "  - dist/$($_.Name)" -ForegroundColor White
        }
    }
    
    # 检查deploy目录
    if (Test-Path "deploy") {
        Write-Host ""
        Write-Host "部署包: deploy/" -ForegroundColor Yellow
    }
    
    # 检查Docker镜像
    try {
        Write-Host ""
        Write-Host "Docker镜像:" -ForegroundColor Yellow
        docker images enhanced-stream-asr --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" 2>$null
    }
    catch {
        Write-Host "无法列出Docker镜像" -ForegroundColor Gray
    }
}

function Show-Usage {
    Write-Step "使用示例"
    
    Write-Host "部署方式:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. 使用Python包:" -ForegroundColor Cyan
    Write-Host "   pip install dist/*.whl" -ForegroundColor Gray
    Write-Host "   enhanced_stream_asr" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. 使用Docker (CPU):" -ForegroundColor Cyan
    Write-Host "   docker run -d -p 8000:8000 -p 8001:8001 enhanced-stream-asr:cpu" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. 使用Docker (GPU):" -ForegroundColor Cyan
    Write-Host "   docker run -d -p 8000:8000 -p 8001:8001 --gpus all enhanced-stream-asr:gpu" -ForegroundColor Gray
    Write-Host ""
    Write-Host "4. 使用Docker Compose:" -ForegroundColor Cyan
    Write-Host "   docker-compose --profile cpu up -d" -ForegroundColor Gray
    Write-Host "   docker-compose --profile gpu up -d" -ForegroundColor Gray
    Write-Host ""
    Write-Host "测试服务:" -ForegroundColor Yellow
    Write-Host "   Invoke-RestMethod http://localhost:8000/health" -ForegroundColor Gray
    Write-Host "   访问 http://localhost:8000 查看Web界面" -ForegroundColor Gray
}

# 主程序
function Main {
    # 显示标题
    Write-Host "Enhanced Stream ASR Server - PowerShell Build Script" -ForegroundColor Cyan
    Write-Host "===================================================" -ForegroundColor Cyan
    
    # 显示帮助
    if ($Help) {
        Show-Help
        return
    }
    
    # 如果没有指定参数，默认构建所有
    if (-not ($All -or $Clean -or $Cython -or $Wheel -or $Docker -or $Deploy)) {
        $All = $true
    }
    
    # 检查环境
    if (-not (Test-Prerequisites)) {
        Write-Host "环境检查失败，请安装必要的工具" -ForegroundColor Red
        return
    }
    
    $success = $true
    
    try {
        # 执行清理
        if ($Clean -or $All) {
            if (-not (Build-Clean)) {
                Write-Host "清理失败，继续执行..." -ForegroundColor Yellow
            }
        }
        
        # 安装依赖 (仅在构建所有内容时)
        if ($All) {
            if (-not (Install-Dependencies)) {
                $success = $false
                return
            }
        }
        
        # 构建Cython扩展
        if ($Cython -or $All) {
            if (-not (Build-Cython)) {
                $success = $false
                return
            }
        }
        
        # 构建wheel包
        if ($Wheel -or $All) {
            if (-not (Build-Wheel)) {
                $success = $false
                return
            }
        }
        
        # 构建Docker镜像
        if ($Docker -or $All) {
            if (-not (Build-Docker)) {
                Write-Host "Docker构建失败，继续执行..." -ForegroundColor Yellow
            }
        }
        
        # 创建部署包
        if ($Deploy -or $All) {
            if (-not (Build-Deploy)) {
                $success = $false
                return
            }
        }
        
        # 显示结果
        if ($success) {
            Show-Results
            Show-Usage
            
            Write-Step "构建完成"
            Write-Host "所有构建步骤已完成！" -ForegroundColor Green
            Write-Host "请参考 BUILD_README.md 获取详细的部署说明。" -ForegroundColor Cyan
        }
    }
    catch {
        Write-Host "构建过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
        $success = $false
    }
    
    if (-not $success) {
        Write-Host "构建失败！" -ForegroundColor Red
        exit 1
    }
}

# 执行主程序
Main
