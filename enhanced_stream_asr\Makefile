# Enhanced Stream ASR Server Makefile
# 简化构建和部署命令

.PHONY: help clean install build build-cython build-wheel build-docker deploy test lint format

# 默认目标
help:
	@echo "Enhanced Stream ASR Server Build Commands"
	@echo "========================================"
	@echo ""
	@echo "Build Commands:"
	@echo "  make build          - Build everything (Cython + Docker + Deploy)"
	@echo "  make build-cython   - Build Cython extensions only"
	@echo "  make build-wheel    - Build wheel package only"
	@echo "  make build-docker   - Build Docker images only"
	@echo "  make clean          - Clean build directories"
	@echo ""
	@echo "Deployment Commands:"
	@echo "  make deploy         - Create deployment package"
	@echo "  make docker-cpu     - Start CPU Docker service"
	@echo "  make docker-gpu     - Start GPU Docker service"
	@echo "  make docker-stop    - Stop Docker services"
	@echo ""
	@echo "Development Commands:"
	@echo "  make install        - Install development dependencies"
	@echo "  make test           - Run tests"
	@echo "  make lint           - Run code linting"
	@echo "  make format         - Format code"
	@echo ""

# 清理构建目录
clean:
	@echo "Cleaning build directories..."
	python build.py --clean

# 安装开发依赖
install:
	@echo "Installing development dependencies..."
	pip install -r requirements.txt
	pip install Cython>=0.29.0 numpy>=1.24.0 setuptools>=65.0.0 wheel>=0.37.0
	pip install pytest black flake8 mypy

# 构建所有内容
build:
	@echo "Building everything..."
	python build.py --all

# 仅构建Cython扩展
build-cython:
	@echo "Building Cython extensions..."
	python build.py --cython

# 仅构建wheel包
build-wheel:
	@echo "Building wheel package..."
	python build.py --wheel

# 仅构建Docker镜像
build-docker:
	@echo "Building Docker images..."
	python build.py --docker

# 创建部署包
deploy:
	@echo "Creating deployment package..."
	python build.py --deploy

# Docker部署命令
docker-cpu:
	@echo "Starting CPU Docker service..."
	docker-compose --profile cpu up -d

docker-gpu:
	@echo "Starting GPU Docker service..."
	docker-compose --profile gpu up -d

docker-monitoring:
	@echo "Starting services with monitoring..."
	docker-compose --profile gpu --profile monitoring up -d

docker-stop:
	@echo "Stopping Docker services..."
	docker-compose down

docker-logs:
	@echo "Showing Docker logs..."
	docker-compose logs -f

# 开发命令
test:
	@echo "Running tests..."
	python -m pytest tests/ -v

test-integration:
	@echo "Running integration tests..."
	python test_asr_integration.py
	python test_system.py

lint:
	@echo "Running code linting..."
	flake8 core/ api/ utils/ web/ --max-line-length=88 --ignore=E203,W503
	mypy core/ api/ utils/ --ignore-missing-imports

format:
	@echo "Formatting code..."
	black core/ api/ utils/ web/ scripts/ tests/ --line-length=88

# 快速开发服务器
dev:
	@echo "Starting development server..."
	python server.py

dev-gradio:
	@echo "Starting Gradio development interface..."
	python start_gradio.py

# 生产部署
prod-install:
	@echo "Installing production package..."
	pip install dist/*.whl

prod-start:
	@echo "Starting production server..."
	enhanced_stream_asr

# 监控和维护
health-check:
	@echo "Checking service health..."
	curl -f http://localhost:8000/health || echo "Service not responding"

logs:
	@echo "Showing application logs..."
	tail -f logs/asr_server.log

# 完整的CI/CD流程
ci: clean install lint test build

# 发布流程
release: clean build test deploy
	@echo "Release package created in deploy/ directory"

# 开发环境设置
setup-dev: install
	@echo "Setting up development environment..."
	pre-commit install || echo "pre-commit not available"
	@echo "Development environment ready!"

# 性能测试
benchmark:
	@echo "Running performance benchmarks..."
	python -m pytest tests/ -k "benchmark" -v

# 安全检查
security:
	@echo "Running security checks..."
	pip install safety bandit
	safety check
	bandit -r core/ api/ utils/ -f json -o security-report.json || true

# 文档生成
docs:
	@echo "Generating documentation..."
	pip install sphinx sphinx-rtd-theme
	sphinx-build -b html docs/ docs/_build/html

# 数据库迁移（如果使用数据库）
migrate:
	@echo "Running database migrations..."
	# 添加数据库迁移命令

# 备份配置
backup-config:
	@echo "Backing up configuration..."
	tar -czf config-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz configs/

# 恢复配置
restore-config:
	@echo "Available config backups:"
	@ls -la config-backup-*.tar.gz 2>/dev/null || echo "No backups found"

# 系统信息
info:
	@echo "System Information:"
	@echo "=================="
	@echo "Python version: $(shell python --version)"
	@echo "Docker version: $(shell docker --version 2>/dev/null || echo 'Docker not installed')"
	@echo "NVIDIA Docker: $(shell nvidia-docker --version 2>/dev/null || echo 'NVIDIA Docker not installed')"
	@echo "Available GPUs: $(shell nvidia-smi -L 2>/dev/null | wc -l || echo '0')"
	@echo "Disk space: $(shell df -h . | tail -1 | awk '{print $$4}') available"
	@echo "Memory: $(shell free -h | grep '^Mem:' | awk '{print $$7}') available"
