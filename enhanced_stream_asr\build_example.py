#!/usr/bin/env python3
"""
Enhanced Stream ASR Server - 构建示例脚本
演示如何使用构建系统
"""

import os
import sys
import subprocess

def print_step(step_name):
    """打印步骤标题"""
    print(f"\n{'='*60}")
    print(f"步骤: {step_name}")
    print('='*60)

def run_command(cmd, description=""):
    """运行命令并显示结果"""
    if description:
        print(f"\n执行: {description}")
    print(f"命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, check=True)
        if result.stdout:
            print("输出:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: 命令执行失败")
        print(f"返回码: {e.returncode}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数 - 演示完整的构建流程"""
    
    print("Enhanced Stream ASR Server - 构建示例")
    print("====================================")
    print("本脚本将演示完整的构建和部署流程")
    
    # 检查环境
    print_step("环境检查")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    if python_version.major != 3 or python_version.minor < 8:
        print("警告: 建议使用Python 3.8或更高版本")
    
    # 检查必要工具
    tools = {
        'docker': 'docker --version',
        'git': 'git --version',
        'make': 'make --version'
    }
    
    for tool, cmd in tools.items():
        if run_command(cmd, f"检查{tool}"):
            print(f"✓ {tool} 可用")
        else:
            print(f"✗ {tool} 不可用")
    
    # 步骤1: 清理环境
    print_step("清理构建环境")
    if not run_command("python build.py --clean", "清理旧的构建文件"):
        print("清理失败，继续执行...")
    
    # 步骤2: 安装依赖
    print_step("安装构建依赖")
    dependencies = [
        "pip install --upgrade pip setuptools wheel",
        "pip install Cython>=0.29.0",
        "pip install numpy>=1.24.0"
    ]
    
    for dep_cmd in dependencies:
        if not run_command(dep_cmd, f"安装依赖"):
            print(f"依赖安装失败: {dep_cmd}")
            return False
    
    # 步骤3: 构建Cython扩展
    print_step("构建Cython扩展")
    if not run_command("python build.py --cython", "编译Cython扩展"):
        print("Cython编译失败")
        return False
    
    # 步骤4: 构建wheel包
    print_step("构建Python包")
    if not run_command("python build.py --wheel", "构建wheel包"):
        print("wheel包构建失败")
        return False
    
    # 步骤5: 构建Docker镜像
    print_step("构建Docker镜像")
    
    # 检查Docker是否可用
    if run_command("docker info", "检查Docker状态"):
        print("Docker可用，开始构建镜像...")
        
        # 构建CPU镜像
        if run_command("docker build -f Dockerfile.cpu -t enhanced-stream-asr:cpu .", "构建CPU镜像"):
            print("✓ CPU镜像构建成功")
        else:
            print("✗ CPU镜像构建失败")
        
        # 检查NVIDIA Docker支持
        if run_command("nvidia-docker --version", "检查NVIDIA Docker"):
            if run_command("docker build -f Dockerfile.gpu -t enhanced-stream-asr:gpu .", "构建GPU镜像"):
                print("✓ GPU镜像构建成功")
            else:
                print("✗ GPU镜像构建失败")
        else:
            print("NVIDIA Docker不可用，跳过GPU镜像构建")
    else:
        print("Docker不可用，跳过镜像构建")
    
    # 步骤6: 创建部署包
    print_step("创建部署包")
    if not run_command("python build.py --deploy", "创建部署包"):
        print("部署包创建失败")
        return False
    
    # 步骤7: 显示构建结果
    print_step("构建结果")
    
    print("构建完成！生成的文件:")
    
    # 检查dist目录
    if os.path.exists('dist'):
        print("\nPython包:")
        for file in os.listdir('dist'):
            print(f"  - dist/{file}")
    
    # 检查deploy目录
    if os.path.exists('deploy'):
        print(f"\n部署包: deploy/")
    
    # 检查Docker镜像
    if run_command("docker images enhanced-stream-asr", "列出Docker镜像"):
        pass
    
    # 步骤8: 使用示例
    print_step("使用示例")
    
    print("部署方式:")
    print("\n1. 使用Python包:")
    print("   pip install dist/*.whl")
    print("   enhanced_stream_asr")
    
    print("\n2. 使用Docker (CPU):")
    print("   docker run -d -p 8000:8000 -p 8001:8001 enhanced-stream-asr:cpu")
    
    print("\n3. 使用Docker (GPU):")
    print("   docker run -d -p 8000:8000 -p 8001:8001 --gpus all enhanced-stream-asr:gpu")
    
    print("\n4. 使用Docker Compose:")
    print("   docker-compose --profile cpu up -d")
    print("   docker-compose --profile gpu up -d")
    
    print("\n5. 使用Makefile:")
    print("   make docker-cpu")
    print("   make docker-gpu")
    
    print("\n测试服务:")
    print("   curl http://localhost:8000/health")
    print("   访问 http://localhost:8000 查看Web界面")
    
    print_step("构建完成")
    print("所有构建步骤已完成！")
    print("请参考 BUILD_README.md 获取详细的部署说明。")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 构建示例执行成功")
            sys.exit(0)
        else:
            print("\n✗ 构建示例执行失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n构建过程中发生错误: {e}")
        sys.exit(1)
