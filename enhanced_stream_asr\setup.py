"""
Enhanced Stream ASR Server Setup Script
用于Cython编译和源码加密封装的安装脚本
"""

import os
import sys
import shutil
from pathlib import Path
from setuptools import setup, find_packages, Extension
from Cython.Build import cythonize
from Cython.Distutils import build_ext
import numpy as np

# 项目信息
PROJECT_NAME = "enhanced_stream_asr"
VERSION = "1.0.0"
DESCRIPTION = "Enhanced Stream ASR Server with Real-time Speech Recognition"
AUTHOR = "ASR Team"
AUTHOR_EMAIL = "<EMAIL>"

# 需要编译的Python文件模式
CYTHON_PATTERNS = [
    "core/**/*.py",
    "api/**/*.py", 
    "utils/**/*.py",
    "web/**/*.py",
    "scripts/**/*.py"
]

# 排除的文件（不进行Cython编译）
EXCLUDE_FILES = [
    "__init__.py",
    "setup.py",
    "server.py",  # 主入口文件保持Python格式
    "start.py",   # 启动脚本保持Python格式
    "start_gradio.py",
    "test_*.py",  # 测试文件
    "fix_*.py"    # 修复脚本
]

def should_compile_file(filepath):
    """判断文件是否应该被Cython编译"""
    filename = os.path.basename(filepath)
    
    # 排除特定文件
    for exclude in EXCLUDE_FILES:
        if exclude.endswith("*.py"):
            if filename.startswith(exclude[:-3]):
                return False
        elif filename == exclude:
            return False
    
    # 只编译.py文件
    return filepath.endswith('.py')

def find_python_files():
    """查找需要编译的Python文件"""
    python_files = []
    
    for pattern in CYTHON_PATTERNS:
        if "**" in pattern:
            # 递归查找
            base_dir = pattern.split("/**")[0]
            if os.path.exists(base_dir):
                for root, dirs, files in os.walk(base_dir):
                    for file in files:
                        filepath = os.path.join(root, file)
                        if should_compile_file(filepath):
                            python_files.append(filepath)
        else:
            # 直接匹配
            if os.path.exists(pattern) and should_compile_file(pattern):
                python_files.append(pattern)
    
    return python_files

def create_extensions():
    """创建Cython扩展"""
    python_files = find_python_files()
    extensions = []
    
    for py_file in python_files:
        # 转换文件路径为模块名
        module_name = py_file.replace(os.sep, '.').replace('.py', '')
        
        # 创建扩展
        ext = Extension(
            module_name,
            [py_file],
            include_dirs=[np.get_include()],
            language_level=3,
            compiler_directives={
                'embedsignature': True,
                'boundscheck': False,
                'wraparound': False,
                'cdivision': True,
                'language_level': 3
            }
        )
        extensions.append(ext)
    
    return extensions

def read_requirements():
    """读取requirements.txt"""
    requirements = []
    if os.path.exists('requirements.txt'):
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 处理注释的依赖
                    if '# ' in line:
                        line = line.split('# ')[0].strip()
                    if line:
                        requirements.append(line)
    
    # 添加Cython依赖
    requirements.extend([
        'Cython>=0.29.0',
        'numpy>=1.24.0'
    ])
    
    return requirements

class CustomBuildExt(build_ext):
    """自定义构建扩展类"""
    
    def run(self):
        # 创建构建目录
        if not os.path.exists(self.build_lib):
            os.makedirs(self.build_lib)
        
        # 运行Cython编译
        super().run()
        
        # 复制非Python文件
        self.copy_non_python_files()
    
    def copy_non_python_files(self):
        """复制配置文件、静态文件等非Python文件"""
        copy_patterns = [
            ('configs/**/*', 'configs/'),
            ('web/static/**/*', 'web/static/'),
            ('docs/**/*', 'docs/'),
            ('*.yaml', './'),
            ('*.yml', './'),
            ('*.json', './'),
            ('*.txt', './')
        ]
        
        for pattern, dest in copy_patterns:
            self.copy_files_by_pattern(pattern, dest)
    
    def copy_files_by_pattern(self, pattern, dest_dir):
        """按模式复制文件"""
        import glob
        
        files = glob.glob(pattern, recursive=True)
        for file_path in files:
            if os.path.isfile(file_path):
                dest_path = os.path.join(self.build_lib, PROJECT_NAME, dest_dir)
                os.makedirs(dest_path, exist_ok=True)
                
                dest_file = os.path.join(dest_path, os.path.basename(file_path))
                shutil.copy2(file_path, dest_file)
                print(f"Copied: {file_path} -> {dest_file}")

# 主要设置
def main():
    # 检查Cython是否可用
    try:
        from Cython.Build import cythonize
    except ImportError:
        print("Error: Cython is required for building this package.")
        print("Please install Cython: pip install Cython")
        sys.exit(1)
    
    # 创建扩展
    extensions = create_extensions()
    
    if not extensions:
        print("Warning: No Python files found for compilation.")
    else:
        print(f"Found {len(extensions)} files to compile with Cython.")
    
    # 读取长描述
    long_description = DESCRIPTION
    if os.path.exists('README.md'):
        with open('README.md', 'r', encoding='utf-8') as f:
            long_description = f.read()
    
    # 设置参数
    setup(
        name=PROJECT_NAME,
        version=VERSION,
        description=DESCRIPTION,
        long_description=long_description,
        long_description_content_type="text/markdown",
        author=AUTHOR,
        author_email=AUTHOR_EMAIL,
        packages=find_packages(),
        ext_modules=cythonize(
            extensions,
            compiler_directives={
                'language_level': 3,
                'embedsignature': True,
                'boundscheck': False,
                'wraparound': False,
                'cdivision': True
            },
            build_dir="build"
        ),
        cmdclass={'build_ext': CustomBuildExt},
        install_requires=read_requirements(),
        python_requires='>=3.8',
        include_package_data=True,
        package_data={
            PROJECT_NAME: [
                'configs/**/*',
                'web/static/**/*',
                'docs/**/*',
                '*.yaml',
                '*.yml', 
                '*.json',
                '*.txt'
            ]
        },
        entry_points={
            'console_scripts': [
                f'{PROJECT_NAME}=server:main',
                f'{PROJECT_NAME}-gradio=start_gradio:main',
            ],
        },
        classifiers=[
            "Development Status :: 4 - Beta",
            "Intended Audience :: Developers",
            "License :: OSI Approved :: MIT License",
            "Programming Language :: Python :: 3",
            "Programming Language :: Python :: 3.8",
            "Programming Language :: Python :: 3.9",
            "Programming Language :: Python :: 3.10",
            "Topic :: Scientific/Engineering :: Artificial Intelligence",
            "Topic :: Multimedia :: Sound/Audio :: Speech",
        ],
        keywords="asr speech-recognition streaming real-time websocket fastapi",
        zip_safe=False,
    )

if __name__ == "__main__":
    main()
