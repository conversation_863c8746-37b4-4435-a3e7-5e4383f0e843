# Enhanced Stream ASR 引擎集成文档

## 📋 概述

本文档详细描述了Enhanced Stream ASR系统中ASR引擎的完整集成实现。基于Individual版本的ASR架构，我们构建了一个企业级的流式语音识别引擎，支持高并发、低延迟的实时语音识别服务。

### 🎯 核心特性

- **🚀 高性能流式处理** - 支持实时音频流识别，延迟 < 100ms
- **🔄 异步架构** - 基于asyncio的高并发处理能力
- **🧠 智能缓存管理** - 滑动窗口和会话池优化内存使用
- **🌍 多语种支持** - 统一接口支持多种语言模型
- **📊 完善监控** - 实时性能指标和错误追踪
- **🔧 灵活配置** - 支持热更新和动态参数调整

## 🏗️ 架构设计

### 核心组件架构

```mermaid
graph TB
    subgraph "ASR引擎层"
        STREAM_ASR[StreamingASREngine]
        MULTI_LANG[MultiLangASR]
        TEXT_PROC[TextProcessor]
    end

    subgraph "特征处理层"
        FEATURE_PIPE[FeaturePipeline]
        SYMBOL_TABLE[SymbolTable]
        AUDIO_PROC[AudioProcessor]
    end

    subgraph "推理引擎层"
        POOLED_ONNX[PooledONNXEngine]
        SESSION_POOL[SessionPool]
        MODEL_MGR[ModelManager]
    end

    subgraph "缓存管理层"
        ENCODER_CACHE[EncoderCache]
        CTC_CACHE[CTCCache]
        BEAM_CACHE[BeamSearchCache]
    end

    STREAM_ASR --> FEATURE_PIPE
    STREAM_ASR --> SYMBOL_TABLE
    MULTI_LANG --> STREAM_ASR
    FEATURE_PIPE --> POOLED_ONNX
    POOLED_ONNX --> SESSION_POOL
    SESSION_POOL --> MODEL_MGR
    STREAM_ASR --> ENCODER_CACHE
    STREAM_ASR --> CTC_CACHE
    STREAM_ASR --> BEAM_CACHE
```

### 核心组件详解

#### 1. **StreamingASREngine** - 流式ASR核心引擎
- 🎯 **职责**: 实时音频流处理和语音识别
- 🔧 **特性**: 支持流式编码器-CTC架构
- 📊 **性能**: 优化的内存管理和缓存机制

#### 2. **SymbolTable** - 符号表管理器
- 🎯 **职责**: Token ID与字符的双向转换
- 🔧 **特性**: 支持多语种字符映射和后处理
- 📊 **性能**: 高效的查找表和缓存机制

#### 3. **FeaturePipeline** - 特征提取管道
- 🎯 **职责**: 音频特征提取和预处理
- 🔧 **特性**: 支持多种特征类型（FBANK、MFCC等）
- 📊 **性能**: 向量化计算和批处理优化

#### 4. **PooledONNXEngine** - 池化ONNX推理引擎
- 🎯 **职责**: 高效的ONNX模型推理管理
- 🔧 **特性**: 会话池管理，支持动态扩缩容
- 📊 **性能**: 会话复用减少初始化开销

### 文件结构

```
enhanced_stream_asr/core/asr/
├── __init__.py                 # 模块导出和版本信息
├── streaming_asr_engine.py    # 🚀 流式ASR引擎核心
├── symbol_table.py            # 🔤 符号表管理
├── feature_pipeline.py        # 🎵 特征提取管道
├── text_processor.py          # 📝 文本后处理
├── multi_lang_asr.py          # 🌍 多语言ASR管理
└── decoder.py                  # 🔧 解码器接口（兼容性）
```

## 主要特性

### 1. 流式处理
- 支持实时音频块处理
- 维护编码器状态缓存
- CTC前缀束搜索解码

### 2. 多语言支持
- 基于配置的语言模型加载
- 语言特定的字符映射和后处理
- 支持热词增强（中文）

### 3. 高性能
- ONNX模型推理
- 会话池管理
- 异步处理

### 4. 可配置性
- 灵活的模型参数配置
- 特征提取参数可调
- 设备选择（CPU/GPU/NPU）

## 配置说明

### 语言配置文件

每种语言都有独立的配置文件，位于 `configs/lang_configs/` 目录：

```yaml
# 中文配置示例 (zh.yaml)
code: "zh"
name: "中文"

model:
  model_path: "models/zh"              # 模型目录
  dict_path: "models/zh/units.txt"     # 词典文件
  hotwords_path: "models/zh/hotwords.txt"  # 热词文件
  
  # 解码参数
  chunk_size: 16
  left_chunks: 16
  decoding_window: 67
  subsampling_rate: 4
  right_context: 7
  
  # 设备设置
  device: "cpu"
  device_id: 0
  quantized: true
  
  # 模型结构参数
  num_blocks: 12
  head: 8
  output_size: 512
  cnn_module_kernel: 15

performance:
  blank_interval: 0.5          # 空白间隔（秒）
```

### 模型文件结构

```
models/
├── zh/                      # 中文模型
│   ├── encoder.onnx        # 编码器模型
│   ├── ctc.onnx           # CTC模型
│   ├── decoder.onnx       # 解码器模型（可选）
│   ├── units.txt          # 词典文件
│   └── hotwords.txt       # 热词文件
└── en/                      # 英文模型
    ├── encoder.onnx
    ├── ctc.onnx
    ├── units.txt
    └── hotwords.txt
```

## 使用方法

### 1. 基本使用

```python
from core.asr import StreamingASREngine

# 创建配置
config = {
    'language': 'zh',
    'model_path': 'models/zh',
    'dict_path': 'models/zh/units.txt',
    'device': 'cpu',
    # ... 其他配置
}

# 创建引擎
engine = StreamingASREngine('models/zh', config)

# 初始化
await engine.initialize()

# 处理音频块
result = await engine.process_audio_chunk(audio_data, is_final=False)
print(result['text'])

# 完成识别
final_result = await engine.finalize()

# 清理资源
await engine.cleanup()
```

### 2. 在SessionManager中的集成

ASR引擎已经集成到SessionManager中，会在语言检测完成后自动初始化：

```python
# SessionManager会自动：
# 1. 根据检测到的语言加载配置
# 2. 创建StreamingASREngine实例
# 3. 初始化引擎
# 4. 在音频处理中调用引擎
# 5. 在会话结束时清理资源
```

## API接口

### StreamingASREngine

#### 主要方法

- `__init__(model_path, config)` - 初始化引擎
- `initialize()` - 异步初始化，加载模型和资源
- `process_audio_chunk(audio_data, is_final)` - 处理音频块
- `finalize()` - 完成识别，返回最终结果
- `cleanup()` - 清理资源
- `is_ready()` - 检查引擎是否就绪

#### 返回格式

```python
{
    "text": "识别的文本",
    "language": "zh",
    "confidence": 0.95,
    "is_final": False,
    "processing_time": 45.2,
    "success": True
}
```

### SymbolTable

#### 主要方法

- `ids2tokens(ids)` - ID列表转换为token列表
- `tokens2ids(tokens)` - token列表转换为ID列表
- `char_map(text)` - 字符映射和后处理

### FeaturePipeline

#### 主要方法

- `extract_features(audio_data)` - 提取音频特征
- `to_waveform(pcm_bytes)` - PCM字节转换为波形张量

## 测试

运行集成测试：

```bash
cd enhanced_stream_asr
python test_asr_integration.py
```

测试包括：
- 符号表功能测试
- 特征提取测试
- 配置加载测试
- ASR引擎基础测试

## 性能优化

### 1. 会话池
- 使用PooledONNXEngine管理ONNX会话
- 支持动态扩缩容
- 会话复用减少初始化开销

### 2. 内存管理
- 及时清理缓存
- 避免内存泄漏
- 合理的缓存大小设置

### 3. 并发处理
- 异步处理音频块
- 线程安全的状态管理
- 非阻塞的推理调用

## 故障排除

### 常见问题

1. **模型文件未找到**
   - 检查模型路径配置
   - 确保模型文件存在

2. **初始化失败**
   - 检查ONNX Runtime安装
   - 验证模型文件完整性
   - 检查设备配置

3. **推理错误**
   - 检查输入数据格式
   - 验证模型兼容性
   - 查看详细错误日志

### 日志调试

启用详细日志：

```python
import logging
logging.getLogger('enhanced_stream_asr').setLevel(logging.DEBUG)
```

## 扩展开发

### 添加新语言支持

1. 创建语言配置文件 `configs/lang_configs/{lang}.yaml`
2. 准备模型文件到 `models/{lang}/`
3. 更新LID模型支持新语言
4. 测试验证

### 自定义特征提取

继承FeaturePipeline类并实现自定义特征提取方法：

```python
class CustomFeaturePipeline(FeaturePipeline):
    def compute_custom_features(self, waveform, sample_rate):
        # 实现自定义特征提取
        pass
```

## 注意事项

1. **模型兼容性** - 确保ONNX模型与当前版本兼容
2. **内存使用** - 监控内存使用，特别是在高并发场景
3. **性能调优** - 根据硬件配置调整参数
4. **错误处理** - 实现完善的错误处理和恢复机制
