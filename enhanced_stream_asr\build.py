#!/usr/bin/env python3
"""
Enhanced Stream ASR Server Build Script
用于自动化构建和打包的脚本
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    print(f"Running: {cmd}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True,
            check=True
        )
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {cmd}")
        print(f"Return code: {e.returncode}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def clean_build():
    """清理构建目录"""
    print("Cleaning build directories...")
    
    dirs_to_clean = [
        'build',
        'dist', 
        '*.egg-info',
        '__pycache__',
        '**/__pycache__',
        '**/*.pyc',
        '**/*.pyo',
        '**/*.so',
        '**/*.c'  # Cython生成的C文件
    ]
    
    for pattern in dirs_to_clean:
        if '*' in pattern:
            import glob
            for path in glob.glob(pattern, recursive=True):
                if os.path.isdir(path):
                    shutil.rmtree(path, ignore_errors=True)
                    print(f"Removed directory: {path}")
                elif os.path.isfile(path):
                    os.remove(path)
                    print(f"Removed file: {path}")
        else:
            if os.path.exists(pattern):
                if os.path.isdir(pattern):
                    shutil.rmtree(pattern, ignore_errors=True)
                    print(f"Removed directory: {pattern}")
                else:
                    os.remove(pattern)
                    print(f"Removed file: {pattern}")

def install_dependencies():
    """安装构建依赖"""
    print("Installing build dependencies...")
    
    dependencies = [
        'Cython>=0.29.0',
        'numpy>=1.24.0',
        'setuptools>=65.0.0',
        'wheel>=0.37.0'
    ]
    
    for dep in dependencies:
        if not run_command(f"pip install {dep}"):
            print(f"Failed to install {dep}")
            return False
    
    return True

def build_cython():
    """构建Cython扩展"""
    print("Building Cython extensions...")
    
    # 构建扩展
    if not run_command("python setup.py build_ext --inplace"):
        print("Failed to build Cython extensions")
        return False
    
    return True

def build_wheel():
    """构建wheel包"""
    print("Building wheel package...")
    
    if not run_command("python setup.py bdist_wheel"):
        print("Failed to build wheel package")
        return False
    
    return True

def build_source():
    """构建源码包"""
    print("Building source package...")
    
    if not run_command("python setup.py sdist"):
        print("Failed to build source package")
        return False
    
    return True

def create_docker_images():
    """构建Docker镜像"""
    print("Building Docker images...")
    
    # 构建CPU版本
    print("Building CPU Docker image...")
    if not run_command("docker build -f Dockerfile.cpu -t enhanced-stream-asr:cpu ."):
        print("Failed to build CPU Docker image")
        return False
    
    # 构建GPU版本
    print("Building GPU Docker image...")
    if not run_command("docker build -f Dockerfile.gpu -t enhanced-stream-asr:gpu ."):
        print("Failed to build GPU Docker image")
        return False
    
    return True

def create_deployment_package():
    """创建部署包"""
    print("Creating deployment package...")
    
    # 创建部署目录
    deploy_dir = "deploy"
    if os.path.exists(deploy_dir):
        shutil.rmtree(deploy_dir)
    os.makedirs(deploy_dir)
    
    # 复制必要文件
    files_to_copy = [
        'dist/',
        'Dockerfile.cpu',
        'Dockerfile.gpu', 
        'requirements.txt',
        'configs/',
        'docs/',
        'README.md'
    ]
    
    for item in files_to_copy:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.copytree(item, os.path.join(deploy_dir, item))
            else:
                shutil.copy2(item, deploy_dir)
            print(f"Copied: {item}")
    
    # 创建部署脚本
    deploy_script = """#!/bin/bash
# Enhanced Stream ASR Server Deployment Script

echo "Enhanced Stream ASR Server Deployment"
echo "====================================="

# 选择部署方式
echo "Select deployment method:"
echo "1) Docker CPU"
echo "2) Docker GPU" 
echo "3) Python Package"
read -p "Enter choice (1-3): " choice

case $choice in
    1)
        echo "Deploying with Docker CPU..."
        docker build -f Dockerfile.cpu -t enhanced-stream-asr:cpu .
        docker run -d -p 8000:8000 -p 8001:8001 --name asr-server-cpu enhanced-stream-asr:cpu
        ;;
    2)
        echo "Deploying with Docker GPU..."
        docker build -f Dockerfile.gpu -t enhanced-stream-asr:gpu .
        docker run -d -p 8000:8000 -p 8001:8001 --gpus all --name asr-server-gpu enhanced-stream-asr:gpu
        ;;
    3)
        echo "Installing Python package..."
        pip install dist/*.whl
        echo "Run 'enhanced_stream_asr' to start the server"
        ;;
    *)
        echo "Invalid choice"
        exit 1
        ;;
esac

echo "Deployment completed!"
"""
    
    with open(os.path.join(deploy_dir, 'deploy.sh'), 'w') as f:
        f.write(deploy_script)
    
    os.chmod(os.path.join(deploy_dir, 'deploy.sh'), 0o755)
    
    print(f"Deployment package created in: {deploy_dir}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Enhanced Stream ASR Server Build Script')
    parser.add_argument('--clean', action='store_true', help='Clean build directories')
    parser.add_argument('--cython', action='store_true', help='Build Cython extensions only')
    parser.add_argument('--wheel', action='store_true', help='Build wheel package only')
    parser.add_argument('--source', action='store_true', help='Build source package only')
    parser.add_argument('--docker', action='store_true', help='Build Docker images only')
    parser.add_argument('--deploy', action='store_true', help='Create deployment package')
    parser.add_argument('--all', action='store_true', help='Build everything')
    
    args = parser.parse_args()
    
    # 如果没有指定参数，默认构建所有
    if not any([args.clean, args.cython, args.wheel, args.source, args.docker, args.deploy]):
        args.all = True
    
    success = True
    
    try:
        if args.clean or args.all:
            clean_build()
        
        if args.all:
            # 安装依赖
            if not install_dependencies():
                success = False
                return
        
        if args.cython or args.all:
            if not build_cython():
                success = False
                return
        
        if args.wheel or args.all:
            if not build_wheel():
                success = False
                return
        
        if args.source or args.all:
            if not build_source():
                success = False
                return
        
        if args.docker or args.all:
            if not create_docker_images():
                success = False
                return
        
        if args.deploy or args.all:
            if not create_deployment_package():
                success = False
                return
        
        if success:
            print("\n" + "="*50)
            print("BUILD COMPLETED SUCCESSFULLY!")
            print("="*50)
            print("Generated files:")
            if os.path.exists('dist'):
                for file in os.listdir('dist'):
                    print(f"  - dist/{file}")
            if os.path.exists('deploy'):
                print(f"  - deploy/ (deployment package)")
            print("\nDocker images:")
            print("  - enhanced-stream-asr:cpu")
            print("  - enhanced-stream-asr:gpu")
        else:
            print("\nBUILD FAILED!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nBuild interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nBuild failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
