version: '3.8'

services:
  # CPU版本ASR服务
  asr-server-cpu:
    build:
      context: .
      dockerfile: Dockerfile.cpu
    image: enhanced-stream-asr:cpu
    container_name: asr-server-cpu
    ports:
      - "8000:8000"  # HTTP API端口
      - "8001:8001"  # WebSocket端口
    volumes:
      - ./models:/app/models:ro          # 模型文件（只读）
      - ./configs:/app/configs:ro        # 配置文件（只读）
      - ./logs:/app/logs                 # 日志目录
      - ./temp:/app/temp                 # 临时文件目录
    environment:
      - PYTHONUNBUFFERED=1
      - ASR_ENV=production
      - LOG_LEVEL=INFO
      - MODEL_PATH=/app/models
      - CONFIG_PATH=/app/configs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - asr-network
    profiles:
      - cpu

  # GPU版本ASR服务
  asr-server-gpu:
    build:
      context: .
      dockerfile: Dockerfile.gpu
    image: enhanced-stream-asr:gpu
    container_name: asr-server-gpu
    ports:
      - "8000:8000"  # HTTP API端口
      - "8001:8001"  # WebSocket端口
    volumes:
      - ./models:/app/models:ro          # 模型文件（只读）
      - ./configs:/app/configs:ro        # 配置文件（只读）
      - ./logs:/app/logs                 # 日志目录
      - ./temp:/app/temp                 # 临时文件目录
    environment:
      - PYTHONUNBUFFERED=1
      - ASR_ENV=production
      - LOG_LEVEL=INFO
      - MODEL_PATH=/app/models
      - CONFIG_PATH=/app/configs
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - asr-network
    profiles:
      - gpu

  # 监控服务 (可选)
  monitoring:
    image: prom/prometheus:latest
    container_name: asr-monitoring
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - asr-network
    profiles:
      - monitoring

  # Grafana仪表板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: asr-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - asr-network
    profiles:
      - monitoring

  # Redis缓存 (可选，用于会话管理)
  redis:
    image: redis:7-alpine
    container_name: asr-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - asr-network
    profiles:
      - cache

networks:
  asr-network:
    driver: bridge

volumes:
  grafana-storage:
  redis-data:
