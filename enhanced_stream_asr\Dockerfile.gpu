# Enhanced Stream ASR Server - GPU版本
FROM nvidia/cuda:11.8-devel-ubuntu20.04

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive
ENV CONDA_DEFAULT_ENV=asr_env
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    cmake \
    git \
    wget \
    curl \
    libsndfile1 \
    libsndfile1-dev \
    ffmpeg \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 安装Miniconda
RUN wget https://repo.anaconda.com/miniconda/Miniconda3-py38_4.12.0-Linux-x86_64.sh -O miniconda.sh && \
    bash miniconda.sh -b -p /opt/conda && \
    rm miniconda.sh

# 添加conda到PATH
ENV PATH="/opt/conda/bin:$PATH"

# 创建conda环境
RUN conda create -n asr_env python=3.8 -y

# 激活环境
SHELL ["conda", "run", "-n", "asr_env", "/bin/bash", "-c"]

# 升级pip和安装wheel
RUN pip install --upgrade pip setuptools wheel

# 复制requirements文件
COPY requirements.txt /app/

# 安装CUDA相关的PyTorch (GPU版本)
RUN pip install torch==2.0.1+cu118 torchaudio==2.0.2+cu118 -f https://download.pytorch.org/whl/torch_stable.html

# 安装ONNX Runtime GPU版本
RUN pip install onnxruntime-gpu==1.18.1

# 安装其他Python依赖
RUN pip install -r requirements.txt

# 复制项目文件
COPY . /app/

# 创建必要的目录
RUN mkdir -p /app/logs /app/models /app/temp

# 设置权限
RUN chmod +x /app/server.py /app/start.py

# 暴露端口
EXPOSE 8000 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["conda", "run", "-n", "asr_env", "python", "server.py"]
