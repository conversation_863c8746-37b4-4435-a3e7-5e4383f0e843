# Enhanced Stream ASR 部署指南

## 📖 目录

1. [部署概述](#部署概述)
2. [系统要求](#系统要求)
3. [环境准备](#环境准备)
4. [安装部署](#安装部署)
5. [配置管理](#配置管理)
6. [容器化部署](#容器化部署)
7. [生产环境优化](#生产环境优化)
8. [监控和维护](#监控和维护)
9. [故障排除](#故障排除)
10. [安全配置](#安全配置)

---

## 🎯 部署概述

Enhanced Stream ASR 支持多种部署方式，从开发环境的简单部署到生产环境的高可用集群部署。

### 部署架构选择

| 部署方式 | 适用场景 | 复杂度 | 可用性 |
|----------|----------|--------|--------|
| **单机部署** | 开发测试 | 低 | 一般 |
| **Docker部署** | 快速部署 | 中 | 良好 |
| **Kubernetes集群** | 生产环境 | 高 | 优秀 |
| **云原生部署** | 大规模应用 | 高 | 优秀 |

---

## 💻 系统要求

### 硬件要求

#### 最低配置
- **CPU**: 4核心 2.0GHz
- **内存**: 8GB RAM
- **存储**: 50GB 可用空间
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 8核心 2.4GHz+ (支持AVX2)
- **内存**: 16GB+ RAM
- **存储**: 100GB+ SSD
- **网络**: 1Gbps+
- **GPU**: NVIDIA GPU (可选，用于加速)

### 软件要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **操作系统** | Ubuntu 20.04+ / CentOS 8+ / Windows 10+ | 推荐Linux |
| **Python** | 3.8+ | 推荐3.9+ |
| **Docker** | 20.10+ | 容器化部署 |
| **Kubernetes** | 1.20+ | 集群部署 |
| **Nginx** | 1.18+ | 负载均衡 |

---

## 🔧 环境准备

### 1. 系统优化

#### Linux系统优化

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget git vim htop

# 优化内核参数
echo 'net.core.rmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 增加文件描述符限制
echo '* soft nofile 65536' | sudo tee -a /etc/security/limits.conf
echo '* hard nofile 65536' | sudo tee -a /etc/security/limits.conf
```

#### 防火墙配置

```bash
# Ubuntu/Debian
sudo ufw allow 8080/tcp
sudo ufw allow 22/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --reload
```

### 2. Python环境

```bash
# 安装Python 3.9
sudo apt install -y python3.9 python3.9-venv python3.9-dev

# 创建虚拟环境
python3.9 -m venv /opt/enhanced-asr/venv
source /opt/enhanced-asr/venv/bin/activate

# 升级pip
pip install --upgrade pip setuptools wheel
```

### 3. 依赖安装

```bash
# 系统依赖
sudo apt install -y \
    build-essential \
    cmake \
    pkg-config \
    libffi-dev \
    libssl-dev \
    libasound2-dev \
    portaudio19-dev

# GPU支持（可选）
# NVIDIA驱动和CUDA工具包
sudo apt install -y nvidia-driver-470 nvidia-cuda-toolkit
```

---

## 🚀 安装部署

### 1. 源码部署

#### 下载源码

```bash
# 创建部署目录
sudo mkdir -p /opt/enhanced-asr
sudo chown $USER:$USER /opt/enhanced-asr
cd /opt/enhanced-asr

# 克隆代码
git clone https://github.com/your-org/enhanced-stream-asr.git
cd enhanced-stream-asr

# 切换到稳定版本
git checkout v1.0.0
```

#### 安装依赖

```bash
# 激活虚拟环境
source /opt/enhanced-asr/venv/bin/activate

# 安装Python依赖
pip install -r requirements.txt

# GPU版本（可选）
pip install onnxruntime-gpu
```

#### 配置文件

```bash
# 复制配置模板
cp configs/server_config.yaml.example configs/server_config.yaml
cp configs/lid_config.yaml.example configs/lid_config.yaml

# 创建日志目录
mkdir -p logs

# 创建模型目录
mkdir -p models/{zh,en,ru,lid}
```

#### 模型文件

```bash
# 下载模型文件（示例）
wget -O models/zh/encoder.onnx "https://models.example.com/zh/encoder.onnx"
wget -O models/zh/ctc.onnx "https://models.example.com/zh/ctc.onnx"
wget -O models/zh/units.txt "https://models.example.com/zh/units.txt"

# 验证模型文件
python -c "
import onnx
model = onnx.load('models/zh/encoder.onnx')
print('模型验证成功')
"
```

### 2. 配置验证

```bash
# 验证配置文件
python -c "
from utils.config import ConfigManager
config = ConfigManager('configs')
print('配置验证成功')
"

# 运行系统测试
python test_system.py
```

### 3. 启动服务

#### 开发模式

```bash
# 直接启动
python start.py --dev

# 指定配置文件
python start.py --config configs/server_config.yaml --debug
```

#### 生产模式

```bash
# 使用Gunicorn
pip install gunicorn uvicorn[standard]

# 启动服务
gunicorn -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8080 \
  --timeout 300 \
  --keep-alive 2 \
  --max-requests 1000 \
  --max-requests-jitter 100 \
  server:app
```

---

## ⚙️ 配置管理

### 1. 服务器配置

```yaml
# configs/server_config.yaml
server:
  host: "0.0.0.0"
  port: 8080
  workers: 4
  timeout: 300
  
audio:
  sample_rate: 16000
  chunk_duration: 0.4
  max_audio_duration: 3600
  
asr:
  supported_languages: ["zh", "en", "ru"]
  enable_auto_language_detection: true
  enable_intermediate_result: true
  
models:
  base_path: "models"
  cache_size: 100
  
monitoring:
  enable_metrics: true
  metrics_port: 9090
  log_level: "INFO"
  
security:
  enable_cors: true
  allowed_origins: ["*"]
  api_key_required: false
```

### 2. 语种配置

```yaml
# configs/lang_configs/zh.yaml
code: "zh"
name: "中文"
separator: "，"
silence_threshold: 0.35

model:
  encoder_path: "models/zh/encoder.onnx"
  ctc_path: "models/zh/ctc.onnx"
  vocabulary_path: "models/zh/units.txt"
  hotwords_path: "models/zh/hotwords.txt"

features:
  enable_punctuation: true
  enable_itn: true
  enable_hotwords: true
  enable_emotion_filter: true

processing:
  beam_size: 10
  max_active: 7000
  lattice_beam: 8.0
```

### 3. 环境变量

```bash
# 创建环境变量文件
cat > .env << EOF
# 服务配置
ASR_HOST=0.0.0.0
ASR_PORT=8080
ASR_WORKERS=4

# 模型路径
ASR_MODEL_PATH=/opt/enhanced-asr/models

# 日志配置
ASR_LOG_LEVEL=INFO
ASR_LOG_FILE=/opt/enhanced-asr/logs/asr.log

# 监控配置
ASR_ENABLE_METRICS=true
ASR_METRICS_PORT=9090

# 安全配置
ASR_API_KEY=your-secret-api-key
ASR_ENABLE_CORS=true
EOF

# 加载环境变量
source .env
```

---

## 🐳 容器化部署

### 1. Docker部署

#### Dockerfile

```dockerfile
# Dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    pkg-config \
    libffi-dev \
    libssl-dev \
    libasound2-dev \
    portaudio19-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p logs models

# 暴露端口
EXPOSE 8080

# 设置环境变量
ENV PYTHONPATH=/app
ENV ASR_CONFIG_PATH=/app/configs

# 启动命令
CMD ["python", "start.py", "--prod"]
```

#### 构建和运行

```bash
# 构建镜像
docker build -t enhanced-stream-asr:latest .

# 运行容器
docker run -d \
  --name asr-server \
  -p 8080:8080 \
  -v $(pwd)/models:/app/models \
  -v $(pwd)/configs:/app/configs \
  -v $(pwd)/logs:/app/logs \
  --restart unless-stopped \
  enhanced-stream-asr:latest

# 查看日志
docker logs -f asr-server

# 进入容器
docker exec -it asr-server bash
```

### 2. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  asr-server:
    build: .
    container_name: enhanced-asr-server
    ports:
      - "8080:8080"
      - "9090:9090"  # 监控端口
    volumes:
      - ./models:/app/models
      - ./configs:/app/configs
      - ./logs:/app/logs
    environment:
      - ASR_HOST=0.0.0.0
      - ASR_PORT=8080
      - ASR_LOG_LEVEL=INFO
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    container_name: enhanced-asr-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - asr-server
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    container_name: enhanced-asr-prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: enhanced-asr-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
    restart: unless-stopped

volumes:
  grafana-data:
```

#### 启动服务栈

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f asr-server

# 停止服务
docker-compose down
```
