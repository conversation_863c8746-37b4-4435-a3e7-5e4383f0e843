# Enhanced Stream ASR Server - PowerShell Deployment Script
# 用于Windows PowerShell的部署脚本

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("cpu", "gpu", "python", "compose")]
    [string]$Method = "",
    
    [switch]$Stop,
    [switch]$Status,
    [switch]$Logs,
    [switch]$Help
)

function Write-Title {
    param([string]$Title)
    Write-Host ""
    Write-Host ("=" * 60) -ForegroundColor Cyan
    Write-Host $Title -ForegroundColor Yellow
    Write-Host ("=" * 60) -ForegroundColor Cyan
}

function Show-Help {
    Write-Title "Enhanced Stream ASR Server - 部署脚本"
    Write-Host ""
    Write-Host "用法: .\deploy.ps1 [方法] [选项]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "部署方法:" -ForegroundColor Yellow
    Write-Host "  cpu       使用Docker CPU版本部署" -ForegroundColor White
    Write-Host "  gpu       使用Docker GPU版本部署" -ForegroundColor White
    Write-Host "  python    使用Python包部署" -ForegroundColor White
    Write-Host "  compose   使用Docker Compose部署" -ForegroundColor White
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  -Stop     停止服务" -ForegroundColor White
    Write-Host "  -Status   查看服务状态" -ForegroundColor White
    Write-Host "  -Logs     查看服务日志" -ForegroundColor White
    Write-Host "  -Help     显示此帮助信息" -ForegroundColor White
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\deploy.ps1 cpu          # 使用Docker CPU版本部署" -ForegroundColor Gray
    Write-Host "  .\deploy.ps1 gpu          # 使用Docker GPU版本部署" -ForegroundColor Gray
    Write-Host "  .\deploy.ps1 -Stop        # 停止所有服务" -ForegroundColor Gray
    Write-Host "  .\deploy.ps1 -Status      # 查看服务状态" -ForegroundColor Gray
}

function Test-Docker {
    try {
        docker info | Out-Null
        return $true
    }
    catch {
        Write-Host "✗ Docker 服务未运行或不可用" -ForegroundColor Red
        return $false
    }
}

function Test-DockerCompose {
    try {
        docker-compose --version | Out-Null
        return $true
    }
    catch {
        Write-Host "✗ Docker Compose 不可用" -ForegroundColor Red
        return $false
    }
}

function Deploy-CPU {
    Write-Title "部署 CPU 版本"
    
    if (-not (Test-Docker)) {
        return $false
    }
    
    Write-Host "检查镜像..." -ForegroundColor Green
    $image = docker images -q enhanced-stream-asr:cpu
    if (-not $image) {
        Write-Host "镜像不存在，开始构建..." -ForegroundColor Yellow
        docker build -f Dockerfile.cpu -t enhanced-stream-asr:cpu .
        if ($LASTEXITCODE -ne 0) {
            Write-Host "✗ 镜像构建失败" -ForegroundColor Red
            return $false
        }
    }
    
    Write-Host "停止现有容器..." -ForegroundColor Green
    docker stop asr-server-cpu 2>$null
    docker rm asr-server-cpu 2>$null
    
    Write-Host "启动新容器..." -ForegroundColor Green
    docker run -d `
        --name asr-server-cpu `
        -p 8000:8000 `
        -p 8001:8001 `
        -v "${PWD}/models:/app/models:ro" `
        -v "${PWD}/configs:/app/configs:ro" `
        -v "${PWD}/logs:/app/logs" `
        enhanced-stream-asr:cpu
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ CPU版本部署成功" -ForegroundColor Green
        Write-Host "服务地址: http://localhost:8000" -ForegroundColor Cyan
        return $true
    } else {
        Write-Host "✗ CPU版本部署失败" -ForegroundColor Red
        return $false
    }
}

function Deploy-GPU {
    Write-Title "部署 GPU 版本"
    
    if (-not (Test-Docker)) {
        return $false
    }
    
    # 检查NVIDIA Docker支持
    try {
        docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi | Out-Null
        Write-Host "✓ NVIDIA Docker 支持可用" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ NVIDIA Docker 支持不可用" -ForegroundColor Red
        Write-Host "请安装 NVIDIA Docker 支持" -ForegroundColor Yellow
        return $false
    }
    
    Write-Host "检查镜像..." -ForegroundColor Green
    $image = docker images -q enhanced-stream-asr:gpu
    if (-not $image) {
        Write-Host "镜像不存在，开始构建..." -ForegroundColor Yellow
        docker build -f Dockerfile.gpu -t enhanced-stream-asr:gpu .
        if ($LASTEXITCODE -ne 0) {
            Write-Host "✗ 镜像构建失败" -ForegroundColor Red
            return $false
        }
    }
    
    Write-Host "停止现有容器..." -ForegroundColor Green
    docker stop asr-server-gpu 2>$null
    docker rm asr-server-gpu 2>$null
    
    Write-Host "启动新容器..." -ForegroundColor Green
    docker run -d `
        --name asr-server-gpu `
        -p 8000:8000 `
        -p 8001:8001 `
        -v "${PWD}/models:/app/models:ro" `
        -v "${PWD}/configs:/app/configs:ro" `
        -v "${PWD}/logs:/app/logs" `
        --gpus all `
        enhanced-stream-asr:gpu
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ GPU版本部署成功" -ForegroundColor Green
        Write-Host "服务地址: http://localhost:8000" -ForegroundColor Cyan
        return $true
    } else {
        Write-Host "✗ GPU版本部署失败" -ForegroundColor Red
        return $false
    }
}

function Deploy-Python {
    Write-Title "部署 Python 包"
    
    # 检查wheel文件
    $wheelFiles = Get-ChildItem "dist/*.whl" -ErrorAction SilentlyContinue
    if (-not $wheelFiles) {
        Write-Host "✗ 未找到wheel包，请先运行构建" -ForegroundColor Red
        Write-Host "运行: .\build.ps1 -Wheel" -ForegroundColor Yellow
        return $false
    }
    
    Write-Host "安装Python包..." -ForegroundColor Green
    $wheelFile = $wheelFiles[0].FullName
    pip install $wheelFile --force-reinstall
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python包安装成功" -ForegroundColor Green
        Write-Host "启动服务: enhanced_stream_asr" -ForegroundColor Cyan
        Write-Host "启动Gradio界面: enhanced_stream_asr-gradio" -ForegroundColor Cyan
        return $true
    } else {
        Write-Host "✗ Python包安装失败" -ForegroundColor Red
        return $false
    }
}

function Deploy-Compose {
    Write-Title "部署 Docker Compose"
    
    if (-not (Test-DockerCompose)) {
        return $false
    }
    
    Write-Host "选择部署配置:" -ForegroundColor Yellow
    Write-Host "1. CPU版本" -ForegroundColor White
    Write-Host "2. GPU版本" -ForegroundColor White
    Write-Host "3. GPU版本 + 监控" -ForegroundColor White
    
    $choice = Read-Host "请选择 (1-3)"
    
    switch ($choice) {
        "1" {
            Write-Host "启动CPU版本..." -ForegroundColor Green
            docker-compose --profile cpu up -d
        }
        "2" {
            Write-Host "启动GPU版本..." -ForegroundColor Green
            docker-compose --profile gpu up -d
        }
        "3" {
            Write-Host "启动GPU版本 + 监控..." -ForegroundColor Green
            docker-compose --profile gpu --profile monitoring up -d
        }
        default {
            Write-Host "无效选择" -ForegroundColor Red
            return $false
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker Compose 部署成功" -ForegroundColor Green
        Write-Host "服务地址: http://localhost:8000" -ForegroundColor Cyan
        if ($choice -eq "3") {
            Write-Host "监控地址: http://localhost:9090 (Prometheus)" -ForegroundColor Cyan
            Write-Host "仪表板: http://localhost:3000 (Grafana)" -ForegroundColor Cyan
        }
        return $true
    } else {
        Write-Host "✗ Docker Compose 部署失败" -ForegroundColor Red
        return $false
    }
}

function Stop-Services {
    Write-Title "停止服务"
    
    Write-Host "停止Docker容器..." -ForegroundColor Green
    docker stop asr-server-cpu asr-server-gpu 2>$null
    docker rm asr-server-cpu asr-server-gpu 2>$null
    
    Write-Host "停止Docker Compose服务..." -ForegroundColor Green
    docker-compose down 2>$null
    
    Write-Host "✓ 所有服务已停止" -ForegroundColor Green
}

function Show-Status {
    Write-Title "服务状态"
    
    Write-Host "Docker容器状态:" -ForegroundColor Yellow
    docker ps -a --filter "name=asr-server" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    Write-Host ""
    Write-Host "Docker Compose服务状态:" -ForegroundColor Yellow
    docker-compose ps 2>$null
    
    Write-Host ""
    Write-Host "服务健康检查:" -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8000/health" -TimeoutSec 5
        Write-Host "✓ 服务正常运行" -ForegroundColor Green
        Write-Host "响应: $($response | ConvertTo-Json)" -ForegroundColor Gray
    }
    catch {
        Write-Host "✗ 服务不可访问" -ForegroundColor Red
    }
}

function Show-Logs {
    Write-Title "服务日志"
    
    Write-Host "选择查看日志:" -ForegroundColor Yellow
    Write-Host "1. Docker CPU容器" -ForegroundColor White
    Write-Host "2. Docker GPU容器" -ForegroundColor White
    Write-Host "3. Docker Compose服务" -ForegroundColor White
    Write-Host "4. 应用日志文件" -ForegroundColor White
    
    $choice = Read-Host "请选择 (1-4)"
    
    switch ($choice) {
        "1" { docker logs -f asr-server-cpu }
        "2" { docker logs -f asr-server-gpu }
        "3" { docker-compose logs -f }
        "4" { 
            if (Test-Path "logs/asr_server.log") {
                Get-Content "logs/asr_server.log" -Tail 50 -Wait
            } else {
                Write-Host "日志文件不存在" -ForegroundColor Red
            }
        }
        default { Write-Host "无效选择" -ForegroundColor Red }
    }
}

# 主程序
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    if ($Stop) {
        Stop-Services
        return
    }
    
    if ($Status) {
        Show-Status
        return
    }
    
    if ($Logs) {
        Show-Logs
        return
    }
    
    if ($Method -eq "") {
        Write-Host "请选择部署方法:" -ForegroundColor Yellow
        Write-Host "1. Docker CPU版本" -ForegroundColor White
        Write-Host "2. Docker GPU版本" -ForegroundColor White
        Write-Host "3. Python包" -ForegroundColor White
        Write-Host "4. Docker Compose" -ForegroundColor White
        
        $choice = Read-Host "请选择 (1-4)"
        
        switch ($choice) {
            "1" { $Method = "cpu" }
            "2" { $Method = "gpu" }
            "3" { $Method = "python" }
            "4" { $Method = "compose" }
            default {
                Write-Host "无效选择" -ForegroundColor Red
                return
            }
        }
    }
    
    $success = $false
    
    switch ($Method) {
        "cpu" { $success = Deploy-CPU }
        "gpu" { $success = Deploy-GPU }
        "python" { $success = Deploy-Python }
        "compose" { $success = Deploy-Compose }
        default {
            Write-Host "未知的部署方法: $Method" -ForegroundColor Red
            Show-Help
            return
        }
    }
    
    if ($success) {
        Write-Host ""
        Write-Host "部署完成！" -ForegroundColor Green
        Write-Host "使用 .\deploy.ps1 -Status 查看服务状态" -ForegroundColor Cyan
        Write-Host "使用 .\deploy.ps1 -Logs 查看服务日志" -ForegroundColor Cyan
        Write-Host "使用 .\deploy.ps1 -Stop 停止服务" -ForegroundColor Cyan
    }
}

# 执行主程序
Main
