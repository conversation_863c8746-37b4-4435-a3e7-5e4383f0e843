@echo off
REM Enhanced Stream ASR Server - Windows Build Script
REM 用于Windows系统的构建脚本

setlocal enabledelayedexpansion

echo Enhanced Stream ASR Server - Windows Build Script
echo ================================================

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM 解析命令行参数
set "BUILD_ALL=0"
set "BUILD_CLEAN=0"
set "BUILD_CYTHON=0"
set "BUILD_WHEEL=0"
set "BUILD_DOCKER=0"
set "BUILD_DEPLOY=0"

if "%1"=="" set "BUILD_ALL=1"
if "%1"=="--all" set "BUILD_ALL=1"
if "%1"=="--clean" set "BUILD_CLEAN=1"
if "%1"=="--cython" set "BUILD_CYTHON=1"
if "%1"=="--wheel" set "BUILD_WHEEL=1"
if "%1"=="--docker" set "BUILD_DOCKER=1"
if "%1"=="--deploy" set "BUILD_DEPLOY=1"
if "%1"=="--help" goto :show_help

REM 如果没有指定参数，显示帮助
if "%BUILD_ALL%"=="0" if "%BUILD_CLEAN%"=="0" if "%BUILD_CYTHON%"=="0" if "%BUILD_WHEEL%"=="0" if "%BUILD_DOCKER%"=="0" if "%BUILD_DEPLOY%"=="0" goto :show_help

REM 执行构建步骤
if "%BUILD_CLEAN%"=="1" goto :clean
if "%BUILD_ALL%"=="1" goto :build_all
if "%BUILD_CYTHON%"=="1" goto :build_cython
if "%BUILD_WHEEL%"=="1" goto :build_wheel
if "%BUILD_DOCKER%"=="1" goto :build_docker
if "%BUILD_DEPLOY%"=="1" goto :build_deploy

goto :end

:show_help
echo Usage: build.bat [option]
echo.
echo Options:
echo   --all      Build everything (default)
echo   --clean    Clean build directories
echo   --cython   Build Cython extensions only
echo   --wheel    Build wheel package only
echo   --docker   Build Docker images only
echo   --deploy   Create deployment package only
echo   --help     Show this help message
echo.
pause
goto :end

:clean
echo Cleaning build directories...
python build.py --clean
if errorlevel 1 (
    echo Error: Clean failed
    pause
    exit /b 1
)
echo Clean completed successfully!
goto :end

:build_all
echo Building everything...
echo.

echo Step 1: Installing dependencies...
pip install Cython>=0.29.0 numpy>=1.24.0 setuptools>=65.0.0 wheel>=0.37.0
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

echo Step 2: Building Cython extensions...
python build.py --cython
if errorlevel 1 (
    echo Error: Cython build failed
    pause
    exit /b 1
)

echo Step 3: Building wheel package...
python build.py --wheel
if errorlevel 1 (
    echo Error: Wheel build failed
    pause
    exit /b 1
)

echo Step 4: Checking Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo Warning: Docker not available, skipping Docker build
    goto :skip_docker
)

echo Step 5: Building Docker images...
python build.py --docker
if errorlevel 1 (
    echo Warning: Docker build failed, continuing...
)

:skip_docker
echo Step 6: Creating deployment package...
python build.py --deploy
if errorlevel 1 (
    echo Error: Deployment package creation failed
    pause
    exit /b 1
)

echo.
echo ================================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ================================================
echo Generated files:
if exist "dist" (
    echo   Python packages in dist/:
    dir /b dist\
)
if exist "deploy" (
    echo   Deployment package in deploy/
)
echo.
echo Docker images:
docker images enhanced-stream-asr 2>nul
echo.
echo Next steps:
echo   1. Install package: pip install dist\*.whl
echo   2. Run server: enhanced_stream_asr
echo   3. Or use Docker: docker run -d -p 8000:8000 enhanced-stream-asr:cpu
echo.
goto :end

:build_cython
echo Building Cython extensions...
python build.py --cython
if errorlevel 1 (
    echo Error: Cython build failed
    pause
    exit /b 1
)
echo Cython build completed successfully!
goto :end

:build_wheel
echo Building wheel package...
python build.py --wheel
if errorlevel 1 (
    echo Error: Wheel build failed
    pause
    exit /b 1
)
echo Wheel build completed successfully!
goto :end

:build_docker
echo Building Docker images...
docker --version >nul 2>&1
if errorlevel 1 (
    echo Error: Docker is not installed or not in PATH
    pause
    exit /b 1
)

python build.py --docker
if errorlevel 1 (
    echo Error: Docker build failed
    pause
    exit /b 1
)
echo Docker build completed successfully!
goto :end

:build_deploy
echo Creating deployment package...
python build.py --deploy
if errorlevel 1 (
    echo Error: Deployment package creation failed
    pause
    exit /b 1
)
echo Deployment package created successfully!
goto :end

:end
echo.
echo Build script finished.
pause
