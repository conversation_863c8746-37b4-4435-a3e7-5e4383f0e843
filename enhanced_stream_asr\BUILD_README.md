# Enhanced Stream ASR Server - 构建和部署指南

本文档介绍如何构建、打包和部署Enhanced Stream ASR Server。

## 文件说明

### Docker文件
- `Dockerfile.cpu` - CPU版本Docker镜像
- `Dockerfile.gpu` - GPU版本Docker镜像（CUDA 11.8）
- `docker-compose.yml` - Docker Compose配置文件
- `.dockerignore` - Docker构建忽略文件

### 构建文件
- `setup.py` - Cython编译和打包配置
- `build.py` - 自动化构建脚本
- `BUILD_README.md` - 本文档

## 环境要求

### 基础环境
- Python 3.8
- Miniconda/Anaconda
- Docker (用于容器化部署)
- NVIDIA Docker (GPU版本)

### 构建依赖
```bash
pip install Cython>=0.29.0 numpy>=1.24.0 setuptools>=65.0.0 wheel>=0.37.0
```

## 构建方式

### 1. 使用自动化构建脚本（推荐）

```bash
# 构建所有内容（Cython编译 + Docker镜像 + 部署包）
python build.py --all

# 仅清理构建目录
python build.py --clean

# 仅构建Cython扩展
python build.py --cython

# 仅构建wheel包
python build.py --wheel

# 仅构建Docker镜像
python build.py --docker

# 仅创建部署包
python build.py --deploy
```

### 2. 手动构建

#### Cython编译
```bash
# 安装构建依赖
pip install Cython numpy setuptools wheel

# 构建扩展
python setup.py build_ext --inplace

# 构建wheel包
python setup.py bdist_wheel

# 构建源码包
python setup.py sdist
```

#### Docker镜像构建
```bash
# CPU版本
docker build -f Dockerfile.cpu -t enhanced-stream-asr:cpu .

# GPU版本
docker build -f Dockerfile.gpu -t enhanced-stream-asr:gpu .
```

## 部署方式

### 1. Docker部署（推荐）

#### 使用Docker Compose

```bash
# CPU版本
docker-compose --profile cpu up -d

# GPU版本
docker-compose --profile gpu up -d

# 包含监控服务
docker-compose --profile gpu --profile monitoring up -d

# 停止服务
docker-compose down
```

#### 直接使用Docker

```bash
# CPU版本
docker run -d \
  -p 8000:8000 -p 8001:8001 \
  -v $(pwd)/models:/app/models:ro \
  -v $(pwd)/configs:/app/configs:ro \
  -v $(pwd)/logs:/app/logs \
  --name asr-server-cpu \
  enhanced-stream-asr:cpu

# GPU版本
docker run -d \
  -p 8000:8000 -p 8001:8001 \
  -v $(pwd)/models:/app/models:ro \
  -v $(pwd)/configs:/app/configs:ro \
  -v $(pwd)/logs:/app/logs \
  --gpus all \
  --name asr-server-gpu \
  enhanced-stream-asr:gpu
```

### 2. Python包部署

```bash
# 安装编译后的包
pip install dist/*.whl

# 启动服务
enhanced_stream_asr

# 或者启动Gradio界面
enhanced_stream_asr-gradio
```

### 3. 源码部署

```bash
# 安装依赖
pip install -r requirements.txt

# 直接运行
python server.py

# 或运行Gradio界面
python start_gradio.py
```

## 配置说明

### 环境变量
- `ASR_ENV` - 运行环境 (development/production)
- `LOG_LEVEL` - 日志级别 (DEBUG/INFO/WARNING/ERROR)
- `MODEL_PATH` - 模型文件路径
- `CONFIG_PATH` - 配置文件路径

### 端口配置
- `8000` - HTTP API端口
- `8001` - WebSocket端口
- `9090` - Prometheus监控端口（可选）
- `3000` - Grafana仪表板端口（可选）

### 目录挂载
- `./models` - 模型文件目录（只读）
- `./configs` - 配置文件目录（只读）
- `./logs` - 日志输出目录
- `./temp` - 临时文件目录

## Cython编译说明

### 编译范围
- `core/**/*.py` - 核心模块
- `api/**/*.py` - API模块
- `utils/**/*.py` - 工具模块
- `web/**/*.py` - Web界面模块
- `scripts/**/*.py` - 脚本模块

### 排除文件
- `__init__.py` - 包初始化文件
- `server.py` - 主入口文件
- `start*.py` - 启动脚本
- `test_*.py` - 测试文件
- `fix_*.py` - 修复脚本

### 编译选项
- `language_level=3` - Python 3语法
- `embedsignature=True` - 嵌入函数签名
- `boundscheck=False` - 禁用边界检查（性能优化）
- `wraparound=False` - 禁用负索引（性能优化）
- `cdivision=True` - C风格除法（性能优化）

## 性能优化

### Docker镜像优化
- 使用多阶段构建减小镜像大小
- 合并RUN指令减少层数
- 使用.dockerignore排除不必要文件
- 使用特定版本标签确保一致性

### Cython优化
- 禁用边界检查和包装检查
- 使用C风格除法
- 嵌入类型信息
- 编译为共享库(.so文件)

## 故障排除

### 常见问题

1. **Cython编译失败**
   ```bash
   # 确保安装了必要的编译工具
   apt-get install build-essential gcc g++ cmake  # Ubuntu/Debian
   yum groupinstall "Development Tools"           # CentOS/RHEL
   ```

2. **Docker GPU支持问题**
   ```bash
   # 安装NVIDIA Docker支持
   curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
   distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
   curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
   sudo apt-get update && sudo apt-get install -y nvidia-docker2
   sudo systemctl restart docker
   ```

3. **模型文件路径问题**
   - 确保模型文件存在于指定路径
   - 检查文件权限
   - 验证配置文件中的路径设置

4. **端口冲突**
   - 修改docker-compose.yml中的端口映射
   - 或使用不同的端口启动服务

### 日志查看

```bash
# Docker容器日志
docker logs asr-server-cpu
docker logs asr-server-gpu

# Docker Compose日志
docker-compose logs -f

# 应用日志
tail -f logs/asr_server.log
```

## 开发建议

1. **开发环境**：使用源码部署方式便于调试
2. **测试环境**：使用Docker部署确保环境一致性
3. **生产环境**：使用编译后的wheel包或Docker镜像
4. **性能测试**：使用GPU版本进行性能基准测试
5. **监控部署**：生产环境建议启用监控服务

## 联系支持

如果在构建或部署过程中遇到问题，请：
1. 检查日志文件获取详细错误信息
2. 确认环境配置是否正确
3. 参考故障排除部分
4. 联系技术支持团队
